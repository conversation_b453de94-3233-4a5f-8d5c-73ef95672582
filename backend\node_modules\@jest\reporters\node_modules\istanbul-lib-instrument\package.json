{"name": "istanbul-lib-instrument", "version": "6.0.3", "description": "Core istanbul API for JS code coverage", "author": "<PERSON><PERSON> <kananthm<PERSON>-<EMAIL>>", "main": "src/index.js", "files": ["src"], "scripts": {"test": "nyc mocha"}, "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "devDependencies": {"@babel/cli": "^7.23.9", "chai": "^4.2.0", "clone": "^2.1.2", "debug": "^4.1.1", "documentation": "^12.1.4", "js-yaml": "^3.13.1", "mocha": "^6.2.3", "nopt": "^4.0.1", "nyc": "^15.1.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-instrument"}, "keywords": ["coverage", "istanbul", "js", "instrumentation"], "engines": {"node": ">=10"}}